// Background script for Prajasakti Epaper Downloader

interface DownloadRequest {
  url: string;
  filename: string;
  cookies: string;
}

interface EpaperData {
  date: string;
  editions: Array<{
    id: string;
    name: string;
    url: string;
  }>;
}

// Listen for messages from popup
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  if (request.action === 'getCookies') {
    getCookiesForSite(request.url)
      .then(cookies => sendResponse({ success: true, cookies }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true; // Keep message channel open for async response
  }

  if (request.action === 'downloadPDF') {
    downloadPDF(request.data)
      .then(result => sendResponse({ success: true, result }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true;
  }

  if (request.action === 'fetchEpaperData') {
    fetchEpaperData(request.url, request.cookies)
      .then(data => sendResponse({ success: true, data }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true;
  }
});

// Get cookies for the specified site
async function getCookiesForSite(url: string): Promise<string> {
  try {
    const cookies = await chrome.cookies.getAll({ url });
    return cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');
  } catch (error) {
    console.error('Error getting cookies:', error);
    throw error;
  }
}

// Download PDF with cookies
async function downloadPDF(data: DownloadRequest): Promise<string> {
  try {
    const response = await fetch(data.url, {
      headers: {
        'Cookie': data.cookies,
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const blob = await response.blob();
    const url = URL.createObjectURL(blob);

    const downloadId = await chrome.downloads.download({
      url: url,
      filename: data.filename,
      saveAs: true
    });

    return `Download started with ID: ${downloadId}`;
  } catch (error) {
    console.error('Error downloading PDF:', error);
    throw error;
  }
}

// Fetch epaper data from the website
async function fetchEpaperData(url: string, cookies: string): Promise<EpaperData> {
  try {
    const response = await fetch(url, {
      headers: {
        'Cookie': cookies,
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const html = await response.text();
    return parseEpaperHTML(html, url);
  } catch (error) {
    console.error('Error fetching epaper data:', error);
    throw error;
  }
}

// Parse HTML to extract edition information and PDF links
function parseEpaperHTML(html: string, baseUrl: string): EpaperData {
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');
  
  // Extract date from URL
  const urlParams = new URLSearchParams(new URL(baseUrl).search);
  const date = urlParams.get('date') || new Date().toISOString().split('T')[0];
  
  // Find edition links
  const editions: Array<{ id: string; name: string; url: string }> = [];
  const editionLinks = doc.querySelectorAll('a[onclick*="active_edition"]');
  
  editionLinks.forEach(link => {
    const onclick = link.getAttribute('onclick');
    const match = onclick?.match(/active_edition\("(\d+)"\)/);
    if (match) {
      const editionId = match[1];
      const editionName = link.textContent?.trim() || `Edition ${editionId}`;
      const editionUrl = `${new URL(baseUrl).origin}/view?date=${date}&edition=${editionId}&pg_no=1`;
      
      editions.push({
        id: editionId,
        name: editionName,
        url: editionUrl
      });
    }
  });

  return {
    date,
    editions: editions.filter((edition, index, self) => 
      index === self.findIndex(e => e.id === edition.id)
    )
  };
}

// Initialize extension
chrome.runtime.onInstalled.addListener(() => {
  console.log('Prajasakti Epaper Downloader installed');
});
