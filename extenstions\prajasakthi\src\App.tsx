import { useState, useEffect } from 'react'
import { Download, Calendar, FolderOpen, AlertCircle, CheckCircle } from 'lucide-react'
import type { EpaperData } from './types'
import { getCookies, fetchEpaperData, downloadPDF, getCurrentTab } from './utils/chrome-api'
import { getTodayDate, getDateRange, formatDateForDisplay } from './utils/date'
import './App.css'

interface DownloadStatus {
  status: 'idle' | 'loading' | 'success' | 'error';
  message: string;
}

function App() {
  const [selectedDate, setSelectedDate] = useState(getTodayDate())
  const [downloadPath, setDownloadPath] = useState('')
  const [epaperData, setEpaperData] = useState<EpaperData | null>(null)
  const [selectedEditions, setSelectedEditions] = useState<Set<string>>(new Set())
  const [downloadStatus, setDownloadStatus] = useState<DownloadStatus>({ status: 'idle', message: '' })
  const [cookies, setCookies] = useState('')
  const [isOnCorrectSite, setIsOnCorrectSite] = useState(false)
  const [manualCookies, setManualCookies] = useState('')
  const [showManualInput, setShowManualInput] = useState(false)

  const dateRange = getDateRange()

  // Check if user is on the correct site
  useEffect(() => {
    checkCurrentSite()
  }, [])

  const checkCurrentSite = async () => {
    try {
      console.log('🌐 Checking current site...')
      const tab = await getCurrentTab()
      console.log('🌐 Current tab URL:', tab.url)

      const isCorrectSite = tab.url?.includes('prajasakti.com') || false
      console.log('🌐 Is on correct site:', isCorrectSite)
      setIsOnCorrectSite(isCorrectSite)

      if (isCorrectSite) {
        console.log('✅ On correct site, auto-extracting cookies...')
        // Auto-extract cookies if on correct site
        await extractCookies()
      } else {
        console.log('⚠️ Not on correct site, skipping auto-extraction')
      }
    } catch (error) {
      console.error('❌ Error checking current site:', error)
    }
  }

  const extractCookies = async () => {
    try {
      console.log('🍪 Starting cookie extraction...')
      setDownloadStatus({ status: 'loading', message: 'Extracting cookies...' })

      const extractedCookies = await getCookies('https://epaper.prajasakti.com/')
      console.log('🍪 Extracted cookies:', extractedCookies ? `${extractedCookies.substring(0, 100)}...` : 'No cookies')
      console.log('🍪 Cookies length:', extractedCookies?.length || 0)

      setCookies(extractedCookies)
      setDownloadStatus({ status: 'success', message: `Cookies extracted successfully! (${extractedCookies?.length || 0} chars)` })

      // Auto-load epaper data
      console.log('📰 Auto-loading epaper data with extracted cookies...')
      await loadEpaperData(extractedCookies)
    } catch (error) {
      console.error('❌ Cookie extraction failed:', error)
      setDownloadStatus({
        status: 'error',
        message: `Failed to extract cookies: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
    }
  }

  const loadEpaperData = async (cookieString?: string) => {
    try {
      console.log('📰 Loading epaper data...')
      setDownloadStatus({ status: 'loading', message: 'Loading epaper data...' })

      const cookiesToUse = cookieString || cookies || manualCookies
      console.log('🍪 Cookies to use:', cookiesToUse ? `${cookiesToUse.substring(0, 100)}...` : 'No cookies')
      console.log('🍪 cookieString param:', cookieString ? `${cookieString.substring(0, 50)}...` : 'undefined')
      console.log('🍪 cookies state:', cookies ? `${cookies.substring(0, 50)}...` : 'empty')
      console.log('🍪 manual cookies:', manualCookies ? `${manualCookies.substring(0, 50)}...` : 'empty')

      if (!cookiesToUse) {
        console.error('❌ No cookies available!')
        throw new Error('No cookies available. Please extract cookies first or enter them manually.')
      }

      const url = `https://epaper.prajasakti.com/view/?date=${selectedDate}&edition=3&pg_no=1`
      console.log('🌐 Fetching URL:', url)

      const data = await fetchEpaperData(url, cookiesToUse)
      console.log('📰 Fetched epaper data:', data)

      setEpaperData(data)
      setDownloadStatus({ status: 'success', message: `Found ${data.editions.length} editions for ${formatDateForDisplay(selectedDate)}` })
    } catch (error) {
      console.error('❌ Failed to load epaper data:', error)
      setDownloadStatus({
        status: 'error',
        message: `Failed to load epaper data: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
    }
  }

  const handleDateChange = (newDate: string) => {
    setSelectedDate(newDate)
    setEpaperData(null)
    setSelectedEditions(new Set())
  }

  const toggleEditionSelection = (editionId: string) => {
    const newSelection = new Set(selectedEditions)
    if (newSelection.has(editionId)) {
      newSelection.delete(editionId)
    } else {
      newSelection.add(editionId)
    }
    setSelectedEditions(newSelection)
  }

  const selectAllEditions = () => {
    if (epaperData) {
      setSelectedEditions(new Set(epaperData.editions.map(e => e.id)))
    }
  }

  const clearSelection = () => {
    setSelectedEditions(new Set())
  }

  const handleDownload = async () => {
    if (!epaperData || selectedEditions.size === 0 || !cookies) {
      setDownloadStatus({ status: 'error', message: 'Please select editions and ensure cookies are available' })
      return
    }

    try {
      setDownloadStatus({ status: 'loading', message: 'Starting downloads...' })

      const selectedEditionsList = epaperData.editions.filter(edition =>
        selectedEditions.has(edition.id)
      )

      let successCount = 0
      let errorCount = 0

      for (const edition of selectedEditionsList) {
        try {
          // First, get the PDF download link for this edition
          const pdfLink = await extractPDFLinkForEdition(edition.url)

          if (pdfLink) {
            const filename = downloadPath
              ? `${downloadPath}/prajasakti_${selectedDate}_edition_${edition.id}_${edition.name.replace(/\s+/g, '_')}.pdf`
              : `prajasakti_${selectedDate}_edition_${edition.id}_${edition.name.replace(/\s+/g, '_')}.pdf`

            await downloadPDF({
              url: pdfLink,
              filename,
              cookies
            })

            successCount++
          } else {
            console.warn(`No PDF link found for edition: ${edition.name}`)
            errorCount++
          }
        } catch (error) {
          console.error(`Error downloading ${edition.name}:`, error)
          errorCount++
        }
      }

      if (successCount > 0) {
        setDownloadStatus({
          status: 'success',
          message: `Successfully started ${successCount} download(s)${errorCount > 0 ? `, ${errorCount} failed` : ''}`
        })
      } else {
        setDownloadStatus({
          status: 'error',
          message: `All downloads failed. Please check if you're on the correct page and try again.`
        })
      }
    } catch (error) {
      setDownloadStatus({
        status: 'error',
        message: `Download failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
    }
  }

  const extractPDFLinkForEdition = async (editionUrl: string): Promise<string | null> => {
    try {
      // Navigate to the edition page and extract PDF link
      const response = await fetch(editionUrl, {
        headers: {
          'Cookie': cookies,
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const html = await response.text()
      const parser = new DOMParser()
      const doc = parser.parseFromString(html, 'text/html')

      // Look for download link
      const downloadLink = doc.querySelector('a[href*="download_epaper"]') as HTMLAnchorElement

      if (downloadLink) {
        const href = downloadLink.href
        // Convert relative URL to absolute if needed
        if (href.startsWith('/')) {
          return new URL(editionUrl).origin + href
        }
        return href
      }

      return null
    } catch (error) {
      console.error('Error extracting PDF link:', error)
      return null
    }
  }

  return (
    <div className="app">
      <header className="app-header">
        <h1>Prajasakti Epaper Downloader</h1>
        <p>Download epaper PDFs with automatic cookie extraction</p>
      </header>

      <main className="app-main">
        {!isOnCorrectSite && (
          <div className="warning-banner">
            <AlertCircle size={20} />
            <span>Please navigate to epaper.prajasakti.com to use this extension</span>
          </div>
        )}

        <div className="form-section">
          <div className="form-group">
            <label htmlFor="date-picker">
              <Calendar size={16} />
              Select Date
            </label>
            <input
              id="date-picker"
              type="date"
              value={selectedDate}
              min={dateRange.min}
              max={dateRange.max}
              onChange={(e) => handleDateChange(e.target.value)}
              className="date-input"
            />
          </div>

          <div className="form-group">
            <label htmlFor="download-path">
              <FolderOpen size={16} />
              Download Path (optional)
            </label>
            <input
              id="download-path"
              type="text"
              value={downloadPath}
              onChange={(e) => setDownloadPath(e.target.value)}
              placeholder="Leave empty to choose during download"
              className="path-input"
            />
          </div>
        </div>

        <div className="actions-section">
          <button
            onClick={extractCookies}
            disabled={downloadStatus.status === 'loading'}
            className="btn btn-primary"
          >
            Extract Cookies & Load Data
          </button>

          <button
            onClick={() => loadEpaperData()}
            disabled={!cookies || downloadStatus.status === 'loading'}
            className="btn btn-secondary"
          >
            Reload Data
          </button>
        </div>

        {!isOnCorrectSite && (
          <div className="manual-cookie-section">
            <p className="manual-cookie-text">
              Not on prajasakti.com? You can still extract cookies manually:
            </p>
            <button
              onClick={extractCookies}
              disabled={downloadStatus.status === 'loading'}
              className="btn btn-outline"
            >
              Force Extract Cookies
            </button>
          </div>
        )}

        {/* Manual Cookie Input */}
        <div className="manual-input-section">
          <button
            onClick={() => setShowManualInput(!showManualInput)}
            className="btn btn-outline btn-small"
          >
            {showManualInput ? 'Hide' : 'Show'} Manual Cookie Input
          </button>

          {showManualInput && (
            <div className="manual-input-content">
              <label htmlFor="manual-cookies">
                Paste cookies from browser DevTools:
              </label>
              <textarea
                id="manual-cookies"
                value={manualCookies}
                onChange={(e) => setManualCookies(e.target.value)}
                placeholder="Paste cookie string here (e.g., _ga=GA1.1.204981852.1749914034; _pk_id=...)"
                className="manual-cookie-textarea"
                rows={4}
              />
              <div className="manual-input-actions">
                <button
                  onClick={() => loadEpaperData(manualCookies)}
                  disabled={!manualCookies.trim() || downloadStatus.status === 'loading'}
                  className="btn btn-primary btn-small"
                >
                  Use Manual Cookies
                </button>
                <button
                  onClick={() => setManualCookies('')}
                  className="btn btn-secondary btn-small"
                >
                  Clear
                </button>
              </div>
              <p className="manual-input-help">
                💡 <strong>How to get cookies:</strong><br/>
                1. Go to epaper.prajasakti.com<br/>
                2. Press F12 → Network tab<br/>
                3. Refresh page → Click any request<br/>
                4. Copy the "Cookie" header value
              </p>
            </div>
          )}
        </div>

        {downloadStatus.message && (
          <div className={`status-message ${downloadStatus.status}`}>
            {downloadStatus.status === 'success' && <CheckCircle size={16} />}
            {downloadStatus.status === 'error' && <AlertCircle size={16} />}
            <span>{downloadStatus.message}</span>
          </div>
        )}

        {/* Debug Panel */}
        <div className="debug-panel">
          <details>
            <summary>🐛 Debug Info</summary>
            <div className="debug-content">
              <p><strong>Current Site:</strong> {isOnCorrectSite ? '✅ epaper.prajasakti.com' : '❌ Wrong site'}</p>
              <p><strong>Auto Cookies:</strong> {cookies ? `✅ ${cookies.length} chars` : '❌ No cookies'}</p>
              <p><strong>Manual Cookies:</strong> {manualCookies ? `✅ ${manualCookies.length} chars` : '❌ No cookies'}</p>
              <p><strong>Selected Date:</strong> {selectedDate}</p>
              <p><strong>Editions Found:</strong> {epaperData ? epaperData.editions.length : 'None'}</p>
              {(cookies || manualCookies) && (
                <div>
                  <strong>Cookie Preview:</strong>
                  <code className="cookie-preview">
                    {(cookies || manualCookies).substring(0, 200)}...
                  </code>
                </div>
              )}
            </div>
          </details>
        </div>

        {epaperData && (
          <div className="editions-section">
            <div className="editions-header">
              <h3>Available Editions ({epaperData.editions.length})</h3>
              <div className="edition-actions">
                <button onClick={selectAllEditions} className="btn btn-small">
                  Select All
                </button>
                <button onClick={clearSelection} className="btn btn-small">
                  Clear
                </button>
              </div>
            </div>

            <div className="editions-list">
              {epaperData.editions.map((edition) => (
                <label key={edition.id} className="edition-item">
                  <input
                    type="checkbox"
                    checked={selectedEditions.has(edition.id)}
                    onChange={() => toggleEditionSelection(edition.id)}
                  />
                  <span className="edition-name">{edition.name}</span>
                  <span className="edition-id">ID: {edition.id}</span>
                </label>
              ))}
            </div>

            {selectedEditions.size > 0 && (
              <div className="download-section">
                <p>{selectedEditions.size} edition(s) selected</p>
                <button
                  onClick={handleDownload}
                  disabled={downloadStatus.status === 'loading'}
                  className="btn btn-download"
                >
                  <Download size={16} />
                  Download Selected PDFs
                </button>
              </div>
            )}
          </div>
        )}
      </main>
    </div>
  )
}

export default App
