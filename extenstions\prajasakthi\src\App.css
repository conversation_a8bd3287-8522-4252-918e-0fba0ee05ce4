/* Chrome Extension Popup Styles */
.app {
  width: 400px;
  min-height: 500px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: #ffffff;
  color: #333;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  text-align: center;
}

.app-header h1 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
}

.app-header p {
  margin: 0;
  font-size: 12px;
  opacity: 0.9;
}

.app-main {
  padding: 20px;
}

.warning-banner {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
  font-size: 13px;
}

.form-section {
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 6px;
  color: #555;
}

.date-input,
.path-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 13px;
  box-sizing: border-box;
}

.date-input:focus,
.path-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.actions-section {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
  justify-content: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #5a6fd8;
}

.btn-secondary {
  background: #f8f9fa;
  color: #495057;
  border: 1px solid #dee2e6;
}

.btn-secondary:hover:not(:disabled) {
  background: #e9ecef;
}

.btn-small {
  padding: 4px 8px;
  font-size: 11px;
}

.btn-download {
  background: #28a745;
  color: white;
  width: 100%;
}

.btn-download:hover:not(:disabled) {
  background: #218838;
}

.status-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border-radius: 4px;
  font-size: 13px;
  margin-bottom: 16px;
}

.status-message.success {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.status-message.error {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.status-message.loading {
  background: #d1ecf1;
  border: 1px solid #bee5eb;
  color: #0c5460;
}

.editions-section {
  border-top: 1px solid #eee;
  padding-top: 16px;
}

.editions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.editions-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.edition-actions {
  display: flex;
  gap: 4px;
}

.editions-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 16px;
}

.edition-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: background-color 0.2s;
}

.edition-item:last-child {
  border-bottom: none;
}

.edition-item:hover {
  background: #f8f9fa;
}

.edition-item input[type="checkbox"] {
  margin-right: 8px;
}

.edition-name {
  flex: 1;
  font-size: 13px;
  font-weight: 500;
}

.edition-id {
  font-size: 11px;
  color: #666;
  background: #f1f3f4;
  padding: 2px 6px;
  border-radius: 3px;
}

.download-section {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 4px;
}

.download-section p {
  margin: 0 0 12px 0;
  font-size: 13px;
  color: #666;
}

/* Debug Panel */
.debug-panel {
  margin-top: 16px;
  border-top: 1px solid #eee;
  padding-top: 16px;
}

.debug-panel details {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px;
}

.debug-panel summary {
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  color: #666;
  user-select: none;
}

.debug-panel summary:hover {
  color: #333;
}

.debug-content {
  margin-top: 8px;
  font-size: 11px;
  line-height: 1.4;
}

.debug-content p {
  margin: 4px 0;
}

.debug-content strong {
  color: #333;
}

.cookie-preview {
  display: block;
  background: #f5f5f5;
  padding: 4px 6px;
  border-radius: 3px;
  font-family: monospace;
  font-size: 10px;
  margin-top: 4px;
  word-break: break-all;
  max-height: 60px;
  overflow-y: auto;
}
