{"version": 3, "names": ["_initializerWarningHelper", "descriptor", "context", "Error"], "sources": ["../../src/helpers/initializerWarningHelper.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\nexport default function _initializerWarningHelper(\n  descriptor: PropertyDescriptor,\n  context: DecoratorContext,\n): never {\n  throw new Error(\n    \"Decorating class property failed. Please ensure that \" +\n      \"transform-class-properties is enabled and runs after the decorators transform.\",\n  );\n}\n"], "mappings": ";;;;;;AAGe,SAASA,yBAAyBA,CAC/CC,UAA8B,EAC9BC,OAAyB,EAClB;EACP,MAAM,IAAIC,KAAK,CACb,uDAAuD,GACrD,gFACJ,CAAC;AACH", "ignoreList": []}