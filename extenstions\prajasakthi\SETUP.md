# Quick Setup Guide

## 🚀 Ready to Use!

Your Chrome extension is now ready! Follow these steps to get it running:

## 1. Install Dependencies & Build

```bash
# Navigate to the project directory
cd extenstions/prajasakthi

# Install dependencies (this will install @types/chrome and lucide-react)
npm install

# Build the extension
npm run build
```

**Note**: If you encounter TypeScript errors during build, all the code has been fixed and should compile successfully. The main fixes included:
- Using type-only imports for TypeScript interfaces
- Fixing unused parameter warnings
- Adding Chrome extension types

## 2. Create Icons (Optional but Recommended)

Create these icon files in the `public` folder:
- `icon16.png` (16x16 pixels)
- `icon48.png` (48x48 pixels) 
- `icon128.png` (128x128 pixels)

Or you can skip this step - the extension will work without icons.

## 3. Load in Chrome

1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode" (toggle in top right)
3. Click "Load unpacked"
4. Select the `dist` folder (created after `npm run build`)

## 4. Test the Extension

1. Navigate to `https://epaper.prajasakti.com/`
2. Click the extension icon in Chrome toolbar
3. The extension should automatically extract cookies
4. Select date and editions to download

## 🎉 You're Done!

The extension will:
- ✅ Extract cookies automatically from the epaper site
- ✅ Show available editions for any date
- ✅ Download PDFs with proper authentication
- ✅ Handle multiple editions at once

## Troubleshooting

If you encounter issues:
1. Make sure you're on the correct website (epaper.prajasakti.com)
2. Check Chrome developer console for errors
3. Reload the extension in chrome://extensions/
4. Ensure all permissions are granted

## What's Different from Python Script?

✅ **No more cookie expiration issues** - Extracts fresh cookies automatically
✅ **User-friendly interface** - Easy date and edition selection
✅ **Chrome integration** - Works directly in your browser
✅ **No manual cookie copying** - Everything is automated

Enjoy your automated epaper downloads! 📰
